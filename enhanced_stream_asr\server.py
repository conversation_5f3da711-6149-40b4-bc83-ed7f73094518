"""
Enhanced Stream ASR Server
增强流式语音识别服务器主程序
"""

import asyncio
import logging
import signal
import sys
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware

from core.session import SessionManager
from api.websocket import WebSocketHandler
from api.http import HTTPEndpoints
from utils.config import ConfigManager
from utils.logger import setup_logger, configure_logging_from_config

# 全局变量
session_manager: SessionManager = None
websocket_handler: WebSocketHandler = None
config_manager: ConfigManager = None
logger = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global session_manager, websocket_handler, config_manager, logger
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager("configs")
        
        # 配置日志
        logger = configure_logging_from_config(config_manager.server_config)
        logger.info("Enhanced Stream ASR Server starting...")
        
        # 初始化会话管理器
        session_manager = SessionManager(config_manager)
        
        # 初始化WebSocket处理器
        websocket_handler = WebSocketHandler(session_manager, config_manager)
        
        logger.info("Server initialization completed")
        
        yield
        
    except Exception as e:
        if logger:
            logger.error(f"Server initialization failed: {e}")
        else:
            print(f"Server initialization failed: {e}")
        raise
    finally:
        # 清理资源
        if logger:
            logger.info("Server shutting down...")
            
        if session_manager:
            await session_manager.shutdown()
            
        if logger:
            logger.info("Server shutdown completed")


# 创建FastAPI应用
app = FastAPI(
    title="Enhanced Stream ASR",
    description="增强流式语音识别服务",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.websocket("/ws/stream")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket流式识别端点"""
    client_id = str(uuid.uuid4())
    
    try:
        await websocket_handler.handle_connection(websocket, client_id)
    except WebSocketDisconnect:
        logger.info(f"WebSocket client disconnected: {client_id}")
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")


@app.get("/")
async def root():
    """根路径，返回Web界面"""
    try:
        with open("web/static/index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(
            content="""
            <html>
                <head><title>Enhanced Stream ASR</title></head>
                <body>
                    <h1>Enhanced Stream ASR Server</h1>
                    <p>Web界面文件未找到。请确保web/static/index.html文件存在。</p>
                    <p>WebSocket端点: /ws/stream</p>
                    <p>API文档: <a href="/docs">/docs</a></p>
                </body>
            </html>
            """,
            status_code=200
        )


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查各组件状态
        health_status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "components": {
                "session_manager": session_manager is not None,
                "websocket_handler": websocket_handler is not None,
                "config_manager": config_manager is not None
            }
        }
        
        # 获取统计信息
        if session_manager:
            health_status["statistics"] = session_manager.get_statistics()
            
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.get("/api/languages")
async def get_supported_languages():
    """获取支持的语种列表"""
    try:
        languages = config_manager.get_supported_languages()
        return {
            "languages": languages,
            "default": config_manager.get("asr.default_language", "zh")
        }
    except Exception as e:
        logger.error(f"Failed to get supported languages: {e}")
        raise HTTPException(status_code=500, detail="Failed to get supported languages")


@app.get("/api/config")
async def get_server_config():
    """获取服务器配置信息"""
    try:
        return {
            "audio": {
                "sample_rate": config_manager.get("audio.sample_rate", 16000),
                "channels": config_manager.get("audio.channels", 1),
                "chunk_duration": config_manager.get("audio.chunk_duration", 0.4),
                "supported_sample_rates": config_manager.get("audio.supported_sample_rates", [16000])
            },
            "asr": {
                "supported_languages": config_manager.get_supported_languages(),
                "enable_auto_language_detection": config_manager.get("asr.enable_auto_language_detection", True),
                "enable_intermediate_result": config_manager.get("asr.enable_intermediate_result", True),
                "enable_punctuation": config_manager.get("asr.enable_punctuation", True)
            },
            "websocket": {
                "heartbeat_interval": config_manager.get("websocket.heartbeat_interval", 30),
                "max_connections": config_manager.get("websocket.max_connections", 100)
            }
        }
    except Exception as e:
        logger.error(f"Failed to get server config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get server config")


@app.get("/api/statistics")
async def get_statistics():
    """获取服务器统计信息"""
    try:
        stats = {}
        
        if session_manager:
            stats["sessions"] = session_manager.get_statistics()
            
        if websocket_handler:
            stats["connections"] = websocket_handler.get_connection_stats()
            
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")


# 挂载静态文件
try:
    app.mount("/static", StaticFiles(directory="web/static"), name="static")
except Exception as e:
    print(f"Warning: Failed to mount static files: {e}")


def signal_handler(signum, frame):
    """信号处理器"""
    if logger:
        logger.info(f"Received signal {signum}, shutting down...")
    else:
        print(f"Received signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 临时配置管理器用于获取服务器配置
        temp_config = ConfigManager("configs")
        
        # 获取服务器配置
        host = temp_config.get("server.host", "0.0.0.0")
        port = temp_config.get("server.port", 8080)
        workers = temp_config.get("server.workers", 1)
        debug = temp_config.get("server.debug", False)
        
        print(f"Starting Enhanced Stream ASR Server on {host}:{port}")
        
        # 启动服务器
        uvicorn.run(
            "server:app",
            host=host,
            port=port,
            workers=workers if not debug else 1,
            reload=debug,
            log_level="info"
        )
        
    except Exception as e:
        print(f"Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
