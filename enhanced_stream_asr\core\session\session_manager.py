"""
会话管理器
管理ASR会话的完整生命周期，包括VAD检测、LID识别、ASR调用
"""

import asyncio
import logging
import time
import uuid
import numpy as np
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from fastapi import WebSocket

from ..lid import <PERSON><PERSON><PERSON>ngine, ProgressiveLID
from ..audio import VADProcessor, FeatureExtractor, AudioUtils
from ..asr import TextProcessor
from ...utils.config import ConfigManager
from ...api.websocket.protocol import HandshakeRequest, AudioDataMessage

logger = logging.getLogger(__name__)


class SessionState(Enum):
    """会话状态枚举"""
    CREATED = "created"
    WAITING_FOR_SPEECH = "waiting_for_speech"
    DETECTING_LANGUAGE = "detecting_language"
    LANGUAGE_DETECTED = "language_detected"
    RECOGNIZING = "recognizing"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class Session:
    """会话数据类"""
    session_id: str
    client_id: str
    websocket: WebSocket
    handshake_request: HandshakeRequest
    state: SessionState
    created_time: float
    last_activity_time: float
    
    # 语种识别相关
    detected_language: Optional[str] = None
    language_confidence: float = 0.0
    progressive_lid: Optional[ProgressiveLID] = None
    
    # ASR相关
    asr_decoder: Optional[Any] = None
    text_processor: Optional[TextProcessor] = None
    
    # 统计信息
    total_audio_chunks: int = 0
    total_audio_duration: float = 0.0
    recognition_count: int = 0
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity_time = time.time()


class SessionManager:
    """会话管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化会话管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.sessions: Dict[str, Session] = {}
        
        # 初始化组件
        self._init_components()
        
        # 启动清理任务
        self.cleanup_task = None
        self.start_cleanup_task()
        
    def _init_components(self):
        """初始化核心组件"""
        try:
            # 初始化VAD处理器
            vad_config = {
                'vad_type': self.config_manager.get('vad.vad_type', 'webrtcvad'),
                'vad_level': self.config_manager.get('vad.vad_level', 1),
                'frame_length': self.config_manager.get('vad.frame_length', 30),
                'window_size': self.config_manager.get('vad.window_size', 10),
                'seg_threshold': self.config_manager.get('vad.seg_threshold', 0.9),
                'sample_rate': self.config_manager.get('audio.sample_rate', 16000)
            }
            self.vad_processor = VADProcessor(vad_config)
            
            # 初始化LID引擎
            lid_config = self.config_manager.get_lid_config()
            if lid_config.get('model', {}).get('model_path'):
                self.lid_engine = LIDEngine(
                    lid_config['model']['model_path'],
                    lid_config
                )
                self.lid_engine.load_model()
            else:
                self.lid_engine = None
                logger.warning("LID model not configured, auto language detection disabled")
                
            # 初始化特征提取器
            feature_config = {
                'feat_type': 'fbank',
                'num_mel_bins': 80,
                'sample_rate': self.config_manager.get('audio.sample_rate', 16000)
            }
            self.feature_extractor = FeatureExtractor(feature_config)
            
            logger.info("Session manager components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize session manager components: {e}")
            raise
            
    async def create_session(
        self, 
        session_id: str, 
        client_id: str, 
        handshake_request: HandshakeRequest,
        websocket: WebSocket
    ) -> bool:
        """
        创建新会话
        
        Args:
            session_id: 会话ID
            client_id: 客户端ID
            handshake_request: 握手请求
            websocket: WebSocket连接
            
        Returns:
            是否创建成功
        """
        try:
            # 检查会话是否已存在
            if session_id in self.sessions:
                logger.warning(f"Session already exists: {session_id}")
                return False
                
            # 创建会话对象
            session = Session(
                session_id=session_id,
                client_id=client_id,
                websocket=websocket,
                handshake_request=handshake_request,
                state=SessionState.CREATED,
                created_time=time.time(),
                last_activity_time=time.time()
            )
            
            # 初始化文本处理器
            session.text_processor = TextProcessor(self.config_manager)
            
            # 如果启用自动语种识别，初始化ProgressiveLID
            if handshake_request.auto_language_detection and self.lid_engine:
                lid_config = self.config_manager.get_lid_config().get('detection', {})
                session.progressive_lid = ProgressiveLID(
                    self.lid_engine, 
                    self.vad_processor, 
                    lid_config
                )
                session.state = SessionState.WAITING_FOR_SPEECH
            else:
                # 使用指定语种
                session.detected_language = handshake_request.language or self.config_manager.get('asr.default_language', 'zh')
                session.language_confidence = 1.0
                session.state = SessionState.LANGUAGE_DETECTED
                
                # 初始化ASR解码器
                await self._init_asr_decoder(session)
                
            # 保存会话
            self.sessions[session_id] = session
            
            logger.info(f"Session created: {session_id} (client: {client_id}, auto_lid: {handshake_request.auto_language_detection})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create session {session_id}: {e}")
            return False
            
    async def process_audio_data(self, session_id: str, audio_message: AudioDataMessage) -> Dict[str, Any]:
        """
        处理音频数据
        
        Args:
            session_id: 会话ID
            audio_message: 音频数据消息
            
        Returns:
            处理结果
        """
        session = self.sessions.get(session_id)
        if not session:
            return {"error": "Session not found", "error_code": 3001}
            
        try:
            session.update_activity()
            session.total_audio_chunks += 1
            
            # 解码音频数据
            if audio_message.audio_data:
                audio_bytes = AudioUtils.decode_base64_audio(audio_message.audio_data)
                audio_array = np.frombuffer(audio_bytes, dtype=np.int16)
                audio_float = AudioUtils.convert_to_float32(audio_array)

                # 确保是numpy数组
                if not isinstance(audio_float, np.ndarray):
                    audio_float = np.array(audio_float)

                # 更新音频时长
                sample_rate = session.handshake_request.sample_rate
                chunk_duration = len(audio_array) / sample_rate
                session.total_audio_duration += chunk_duration
            else:
                audio_float = np.array([], dtype=np.float32)
                
            # 根据会话状态处理音频
            if session.state == SessionState.WAITING_FOR_SPEECH:
                return await self._handle_waiting_for_speech(session, audio_float, sample_rate)
            elif session.state == SessionState.DETECTING_LANGUAGE:
                return await self._handle_detecting_language(session, audio_float, sample_rate)
            elif session.state == SessionState.RECOGNIZING:
                return await self._handle_recognizing(session, audio_float, audio_message)
            else:
                return {"error": f"Invalid session state: {session.state}", "error_code": 3003}
                
        except Exception as e:
            logger.error(f"Error processing audio data for session {session_id}: {e}")
            session.state = SessionState.ERROR
            return {"error": str(e), "error_code": 2004}
            
    async def _handle_waiting_for_speech(self, session: Session, audio_data: np.ndarray, sample_rate: int) -> Dict[str, Any]:
        """处理等待语音状态"""
        if not session.progressive_lid:
            return {"error": "Progressive LID not initialized", "error_code": 4001}
            
        try:
            # 添加音频到ProgressiveLID
            lid_result = session.progressive_lid.add_audio_chunk(audio_data, sample_rate)
            
            if lid_result:
                if lid_result.language:
                    # 语种检测成功
                    session.detected_language = lid_result.language
                    session.language_confidence = lid_result.confidence
                    session.state = SessionState.LANGUAGE_DETECTED
                    
                    logger.info(f"Language detected for session {session.session_id}: "
                              f"{lid_result.language} (confidence: {lid_result.confidence:.3f})")
                    
                    # 初始化ASR解码器
                    await self._init_asr_decoder(session)
                    
                    return {
                        "language_detected": True,
                        "language": lid_result.language,
                        "confidence": lid_result.confidence,
                        "is_final": lid_result.is_final
                    }
                elif lid_result.is_final:
                    # 检测失败，使用回退语种
                    session.detected_language = lid_result.language or self.config_manager.get('asr.default_language', 'zh')
                    session.language_confidence = lid_result.confidence
                    session.state = SessionState.LANGUAGE_DETECTED
                    
                    await self._init_asr_decoder(session)
                    
                    return {
                        "language_detected": True,
                        "language": session.detected_language,
                        "confidence": session.language_confidence,
                        "is_final": True,
                        "fallback": True
                    }
                    
            return {"waiting_for_speech": True}
            
        except Exception as e:
            logger.error(f"Error in waiting for speech: {e}")
            session.state = SessionState.ERROR
            return {"error": str(e), "error_code": 4001}

    async def _handle_detecting_language(self, session: Session, audio_data: np.ndarray, sample_rate: int) -> Dict[str, Any]:
        """处理语种检测状态"""
        # 这个状态主要用于过渡，通常会很快转到其他状态
        return {"detecting_language": True}

    async def _handle_recognizing(self, session: Session, audio_data: np.ndarray, audio_message: AudioDataMessage) -> Dict[str, Any]:
        """处理语音识别状态"""
        if not session.asr_decoder:
            return {"error": "ASR decoder not initialized", "error_code": 4003}

        try:
            # 这里应该调用ASR解码器进行识别
            # 由于ASR解码器还没有实现，先返回模拟结果

            # 模拟识别结果
            if len(audio_data) > 0:
                session.recognition_count += 1

                # 模拟文本结果
                mock_text = f"识别结果 {session.recognition_count}"
                if session.detected_language == 'en':
                    mock_text = f"Recognition result {session.recognition_count}"

                # 使用文本处理器处理结果
                if session.text_processor and session.detected_language:
                    processed_text = session.text_processor.process_recognition_result(
                        text=mock_text,
                        language=session.detected_language,
                        timestamp=time.time(),
                        confidence=0.9,
                        is_final=audio_message.is_final,
                        custom_separator=session.handshake_request.custom_separator
                    )
                else:
                    processed_text = mock_text

                return {
                    "text": processed_text,
                    "language": session.detected_language,
                    "confidence": 0.9,
                    "is_final": audio_message.is_final,
                    "processing_time": 50.0  # 模拟处理时间
                }
            else:
                return {"text": "", "is_final": audio_message.is_final}

        except Exception as e:
            logger.error(f"Error in recognizing: {e}")
            return {"error": str(e), "error_code": 2001}

    async def _init_asr_decoder(self, session: Session):
        """初始化ASR解码器"""
        try:
            # 获取语种配置
            if not session.detected_language:
                raise ValueError("No language detected")

            lang_config = self.config_manager.get_language_config(session.detected_language)

            if not lang_config:
                logger.warning(f"No configuration found for language: {session.detected_language}")
                # 使用默认配置
                lang_config = {}

            # 这里应该初始化实际的ASR解码器
            # 由于ASR解码器还没有实现，先创建一个模拟对象
            session.asr_decoder = {"language": session.detected_language, "config": lang_config}
            session.state = SessionState.RECOGNIZING

            logger.info(f"ASR decoder initialized for session {session.session_id} (language: {session.detected_language})")

        except Exception as e:
            logger.error(f"Failed to initialize ASR decoder: {e}")
            session.state = SessionState.ERROR
            raise

    async def start_recording(self, session_id: str) -> bool:
        """开始录音"""
        session = self.sessions.get(session_id)
        if not session:
            return False

        try:
            session.update_activity()

            # 重置文本处理器
            if session.text_processor:
                session.text_processor.reset()

            # 重置ProgressiveLID（如果有）
            if session.progressive_lid:
                session.progressive_lid.reset()
                session.state = SessionState.WAITING_FOR_SPEECH
            else:
                session.state = SessionState.RECOGNIZING

            logger.info(f"Recording started for session: {session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to start recording for session {session_id}: {e}")
            return False

    async def stop_recording(self, session_id: str) -> Optional[Dict[str, Any]]:
        """停止录音"""
        session = self.sessions.get(session_id)
        if not session:
            return None

        try:
            session.update_activity()

            # 如果正在进行语种检测，强制完成
            if session.progressive_lid and session.state in [SessionState.WAITING_FOR_SPEECH, SessionState.DETECTING_LANGUAGE]:
                lid_result = session.progressive_lid.force_complete()
                if lid_result and lid_result.language:
                    session.detected_language = lid_result.language
                    session.language_confidence = lid_result.confidence
                    await self._init_asr_decoder(session)

            # 获取最终识别结果
            final_result = None
            if session.text_processor:
                stats = session.text_processor.get_statistics()
                final_result = {
                    "session_id": session_id,
                    "language": session.detected_language,
                    "language_confidence": session.language_confidence,
                    "total_audio_duration": session.total_audio_duration,
                    "recognition_count": session.recognition_count,
                    "statistics": stats
                }

            session.state = SessionState.COMPLETED
            logger.info(f"Recording stopped for session: {session_id}")

            return final_result

        except Exception as e:
            logger.error(f"Failed to stop recording for session {session_id}: {e}")
            session.state = SessionState.ERROR
            return None

    def get_session(self, session_id: str) -> Optional[Session]:
        """获取会话"""
        return self.sessions.get(session_id)

    def get_all_sessions(self) -> List[Session]:
        """获取所有会话"""
        return list(self.sessions.values())

    def get_session_count(self) -> int:
        """获取会话数量"""
        return len(self.sessions)

    async def cleanup_session(self, session_id: str):
        """清理会话"""
        session = self.sessions.get(session_id)
        if session:
            try:
                # 清理资源
                if session.progressive_lid:
                    session.progressive_lid = None
                if session.asr_decoder:
                    session.asr_decoder = None
                if session.text_processor:
                    session.text_processor = None

                # 移除会话
                del self.sessions[session_id]

                logger.info(f"Session cleaned up: {session_id}")

            except Exception as e:
                logger.error(f"Error cleaning up session {session_id}: {e}")

    def start_cleanup_task(self):
        """启动清理任务"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())

    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次

                current_time = time.time()
                timeout = self.config_manager.get('websocket.connection_timeout', 300)

                expired_sessions = []
                for session_id, session in self.sessions.items():
                    if current_time - session.last_activity_time > timeout:
                        expired_sessions.append(session_id)

                # 清理过期会话
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session: {session_id}")
                    await self.cleanup_session(session_id)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    def stop_cleanup_task(self):
        """停止清理任务"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            self.cleanup_task = None

    async def shutdown(self):
        """关闭会话管理器"""
        try:
            # 停止清理任务
            self.stop_cleanup_task()

            # 清理所有会话
            session_ids = list(self.sessions.keys())
            for session_id in session_ids:
                await self.cleanup_session(session_id)

            # 清理组件
            if self.lid_engine:
                self.lid_engine.unload_model()

            logger.info("Session manager shutdown completed")

        except Exception as e:
            logger.error(f"Error during session manager shutdown: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_audio_duration = sum(s.total_audio_duration for s in self.sessions.values())
        total_recognition_count = sum(s.recognition_count for s in self.sessions.values())

        state_counts = {}
        for session in self.sessions.values():
            state = session.state.value
            state_counts[state] = state_counts.get(state, 0) + 1

        return {
            "total_sessions": len(self.sessions),
            "total_audio_duration": total_audio_duration,
            "total_recognition_count": total_recognition_count,
            "session_states": state_counts,
            "lid_engine_ready": self.lid_engine.is_ready() if self.lid_engine else False
        }
