"""
ONNX Runtime推理引擎
基于Individual版本的ONNX推理实现
"""

import os
import logging
from typing import Dict, Any, Union, List
import numpy as np
import torch
import onnxruntime as ort

from .base_engine import BaseEngine

logger = logging.getLogger(__name__)


class ONNXEngine(BaseEngine):
    """ONNX Runtime推理引擎"""
    
    def __init__(self, model_path: str, config: Dict[str, Any]):
        """
        初始化ONNX引擎
        
        Args:
            model_path: ONNX模型目录路径
            config: 引擎配置
        """
        super().__init__(model_path, config)
        
        # ONNX模型文件路径
        self.encoder_path = os.path.join(model_path, "encoder.onnx")
        self.ctc_path = os.path.join(model_path, "ctc.onnx")
        self.decoder_path = os.path.join(model_path, "decoder.onnx")
        
        # ONNX会话
        self.encoder_session = None
        self.ctc_session = None
        self.decoder_session = None
        
        # 设备配置
        self.device = config.get("device", "cpu")
        self.device_id = config.get("device_id", 0)
        self.use_quantized = config.get("quantized", False)
        
        # 推理配置
        self.chunk_size = config.get("chunk_size", 16)
        self.left_chunks = config.get("left_chunks", 16)
        self.decoding_window = config.get("decoding_window", 67)
        
    def load_model(self) -> bool:
        """加载ONNX模型"""
        try:
            # 设置ONNX Runtime提供者
            providers = self._get_providers()
            
            # 加载编码器
            if os.path.exists(self.encoder_path):
                self.encoder_session = ort.InferenceSession(
                    self.encoder_path, providers=providers
                )
                logger.info(f"Loaded encoder: {self.encoder_path}")
            else:
                logger.error(f"Encoder not found: {self.encoder_path}")
                return False
                
            # 加载CTC
            if os.path.exists(self.ctc_path):
                self.ctc_session = ort.InferenceSession(
                    self.ctc_path, providers=providers
                )
                logger.info(f"Loaded CTC: {self.ctc_path}")
            else:
                logger.error(f"CTC not found: {self.ctc_path}")
                return False
                
            # 加载解码器（可选）
            if os.path.exists(self.decoder_path):
                self.decoder_session = ort.InferenceSession(
                    self.decoder_path, providers=providers
                )
                logger.info(f"Loaded decoder: {self.decoder_path}")
                
            self.is_loaded = True
            logger.info("ONNX models loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load ONNX models: {e}")
            return False
            
    def unload_model(self) -> bool:
        """卸载ONNX模型"""
        try:
            if self.encoder_session:
                del self.encoder_session
                self.encoder_session = None
                
            if self.ctc_session:
                del self.ctc_session
                self.ctc_session = None
                
            if self.decoder_session:
                del self.decoder_session
                self.decoder_session = None
                
            self.is_loaded = False
            logger.info("ONNX models unloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload ONNX models: {e}")
            return False
            
    def infer(self, inputs: Dict[str, Union[np.ndarray, torch.Tensor]]) -> Dict[str, Any]:
        """
        执行ONNX推理
        
        Args:
            inputs: 输入数据，包含特征数据
            
        Returns:
            Dict[str, Any]: 推理结果
        """
        if not self.is_ready():
            raise RuntimeError("ONNX engine is not ready")
            
        try:
            # 获取输入特征
            features = inputs.get("features")
            if features is None:
                raise ValueError("Missing 'features' in inputs")
                
            # 转换为numpy数组
            if isinstance(features, torch.Tensor):
                features = features.numpy()
                
            # 编码器推理
            encoder_outputs = self._run_encoder(features)
            
            # CTC推理
            ctc_outputs = self._run_ctc(encoder_outputs)
            
            # 解码器推理（如果有）
            decoder_outputs = None
            if self.decoder_session:
                decoder_outputs = self._run_decoder(encoder_outputs, ctc_outputs)
                
            return {
                "encoder_outputs": encoder_outputs,
                "ctc_outputs": ctc_outputs,
                "decoder_outputs": decoder_outputs,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"ONNX inference failed: {e}")
            return {
                "error": str(e),
                "success": False
            }
            
    def is_ready(self) -> bool:
        """检查引擎是否就绪"""
        return (self.is_loaded and 
                self.encoder_session is not None and 
                self.ctc_session is not None)
                
    def _get_providers(self) -> List[str]:
        """获取ONNX Runtime提供者"""
        if self.device == "gpu":
            return [
                ('CUDAExecutionProvider', {
                    'device_id': self.device_id,
                    'arena_extend_strategy': 'kNextPowerOfTwo',
                    'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB
                    'cudnn_conv_algo_search': 'EXHAUSTIVE',
                    'do_copy_in_default_stream': True,
                }),
                'CPUExecutionProvider'
            ]
        elif self.device == "npu":
            return ['NPUExecutionProvider', 'CPUExecutionProvider']
        else:
            return ['CPUExecutionProvider']
            
    def _run_encoder(self, features: np.ndarray) -> np.ndarray:
        """运行编码器"""
        input_name = self.encoder_session.get_inputs()[0].name
        output_name = self.encoder_session.get_outputs()[0].name
        
        result = self.encoder_session.run(
            [output_name], 
            {input_name: features}
        )
        return result[0]
        
    def _run_ctc(self, encoder_outputs: np.ndarray) -> np.ndarray:
        """运行CTC"""
        input_name = self.ctc_session.get_inputs()[0].name
        output_name = self.ctc_session.get_outputs()[0].name
        
        result = self.ctc_session.run(
            [output_name], 
            {input_name: encoder_outputs}
        )
        return result[0]
        
    def _run_decoder(self, encoder_outputs: np.ndarray, ctc_outputs: np.ndarray) -> np.ndarray:
        """运行解码器"""
        if not self.decoder_session:
            return None
            
        # 这里需要根据具体的解码器输入格式调整
        input_names = [inp.name for inp in self.decoder_session.get_inputs()]
        output_name = self.decoder_session.get_outputs()[0].name
        
        inputs_dict = {}
        if len(input_names) >= 2:
            inputs_dict[input_names[0]] = encoder_outputs
            inputs_dict[input_names[1]] = ctc_outputs
        else:
            inputs_dict[input_names[0]] = encoder_outputs
            
        result = self.decoder_session.run([output_name], inputs_dict)
        return result[0]
