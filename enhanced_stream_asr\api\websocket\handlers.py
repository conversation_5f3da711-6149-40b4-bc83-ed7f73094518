"""
WebSocket处理器
实现完整的三阶段连接处理逻辑
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect

from .protocol import (
    WebSocketProtocol, MessageType, ConnectionState,
    HandshakeRequest, AudioDataMessage
)

logger = logging.getLogger(__name__)


class WebSocketHandler:
    """WebSocket连接处理器"""
    
    def __init__(self, session_manager, config_manager):
        """
        初始化WebSocket处理器
        
        Args:
            session_manager: 会话管理器
            config_manager: 配置管理器
        """
        self.session_manager = session_manager
        self.config_manager = config_manager
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_states: Dict[str, ConnectionState] = {}
        self.heartbeat_interval = config_manager.get("websocket.heartbeat_interval", 30)
        
    async def handle_connection(self, websocket: WebSocket, client_id: str):
        """
        处理WebSocket连接的完整生命周期
        
        Args:
            websocket: WebSocket连接对象
            client_id: 客户端ID
        """
        session_id = None
        
        try:
            # 接受WebSocket连接
            await websocket.accept()
            logger.info(f"WebSocket connection accepted for client: {client_id}")
            
            # 初始化连接状态
            self.active_connections[client_id] = websocket
            self.connection_states[client_id] = ConnectionState.HANDSHAKING
            
            # 等待握手请求
            session_id = await self._handle_handshake_phase(websocket, client_id)
            
            if session_id:
                # 进入数据传输阶段
                await self._handle_data_phase(websocket, client_id, session_id)
            
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for client: {client_id}")
        except Exception as e:
            logger.error(f"WebSocket handler error for client {client_id}: {e}")
            # 发送错误消息
            if session_id:
                await self._send_error(websocket, session_id, 4001, "internal_error", str(e))
        finally:
            # 清理资源
            await self._cleanup_connection(client_id, session_id)
            
    async def _handle_handshake_phase(self, websocket: WebSocket, client_id: str) -> Optional[str]:
        """
        处理握手阶段
        
        Args:
            websocket: WebSocket连接
            client_id: 客户端ID
            
        Returns:
            Optional[str]: 会话ID，如果握手失败返回None
        """
        try:
            # 等待握手请求（超时30秒）
            message_str = await asyncio.wait_for(
                websocket.receive_text(), 
                timeout=30.0
            )
            
            # 解析握手请求
            msg_type, data = WebSocketProtocol.parse_message(message_str)
            
            if msg_type != MessageType.HANDSHAKE_REQUEST:
                await self._send_error(websocket, None, 5003, "protocol_violation", 
                                     "Expected handshake request")
                return None
                
            # 验证握手请求
            is_valid, error_msg = WebSocketProtocol.validate_handshake_request(data)
            if not is_valid:
                await self._send_error(websocket, None, 1002, "invalid_handshake", error_msg or "Invalid handshake")
                return None
                
            # 创建会话
            session_id = str(uuid.uuid4())
            handshake_req = HandshakeRequest(**data)
            
            # 初始化会话
            success = await self.session_manager.create_session(
                session_id, client_id, handshake_req, websocket
            )
            
            if not success:
                await self._send_error(websocket, session_id, 3004, "session_creation_failed", 
                                     "Failed to create session")
                return None
                
            # 发送握手响应
            server_config = {
                "supported_languages": self.config_manager.get("supported_languages", ["zh", "en"]),
                "max_audio_duration": self.config_manager.get("max_audio_duration", 60),
                "chunk_duration": handshake_req.chunk_duration
            }
            
            response = WebSocketProtocol.create_handshake_response(
                session_id, True, "Handshake successful", server_config
            )
            await websocket.send_text(response)
            
            # 更新连接状态
            self.connection_states[client_id] = ConnectionState.CONNECTED
            
            # 发送状态更新
            status_msg = WebSocketProtocol.create_status_update(
                session_id, ConnectionState.CONNECTED, "Session ready"
            )
            await websocket.send_text(status_msg)
            
            logger.info(f"Handshake completed for session: {session_id}")
            return session_id
            
        except asyncio.TimeoutError:
            await self._send_error(websocket, None, 5004, "handshake_timeout", 
                                 "Handshake timeout")
            return None
        except Exception as e:
            logger.error(f"Handshake error: {e}")
            await self._send_error(websocket, None, 1001, "handshake_error", str(e))
            return None

    async def _handle_data_phase(self, websocket: WebSocket, client_id: str, session_id: str):
        """
        处理数据传输阶段

        Args:
            websocket: WebSocket连接
            client_id: 客户端ID
            session_id: 会话ID
        """
        try:
            # 更新连接状态
            self.connection_states[client_id] = ConnectionState.RECORDING

            # 启动心跳任务
            heartbeat_task = asyncio.create_task(
                self._heartbeat_loop(websocket, session_id)
            )

            # 主消息处理循环
            while True:
                try:
                    # 接收消息（带超时）
                    message_str = await asyncio.wait_for(
                        websocket.receive_text(),
                        timeout=self.heartbeat_interval * 2
                    )

                    # 解析消息
                    msg_type, data = WebSocketProtocol.parse_message(message_str)

                    # 处理不同类型的消息
                    if msg_type == MessageType.AUDIO_DATA:
                        await self._handle_audio_data(websocket, session_id, data)
                    elif msg_type == MessageType.START_RECORDING:
                        await self._handle_start_recording(websocket, session_id)
                    elif msg_type == MessageType.STOP_RECORDING:
                        await self._handle_stop_recording(websocket, session_id)
                    elif msg_type == MessageType.DISCONNECT_REQUEST:
                        await self._handle_disconnect_request(websocket, session_id)
                        break
                    elif msg_type == MessageType.HEARTBEAT:
                        await self._handle_heartbeat(websocket, session_id)
                    else:
                        logger.warning(f"Unsupported message type: {msg_type}")
                        await self._send_error(websocket, session_id, 5002,
                                             "unsupported_message", f"Unsupported message type: {msg_type}")

                except asyncio.TimeoutError:
                    logger.warning(f"Message timeout for session: {session_id}")
                    await self._send_error(websocket, session_id, 5004,
                                         "message_timeout", "Message receive timeout")
                    break
                except WebSocketDisconnect:
                    logger.info(f"Client disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Message handling error: {e}")
                    await self._send_error(websocket, session_id, 4001,
                                         "message_error", str(e))

            # 取消心跳任务
            heartbeat_task.cancel()

        except Exception as e:
            logger.error(f"Data phase error: {e}")

    async def _handle_audio_data(self, websocket: WebSocket, session_id: str, data: Dict[str, Any]):
        """处理音频数据消息"""
        try:
            # 解析音频数据消息
            audio_msg = AudioDataMessage(**data)

            # 验证会话
            session = self.session_manager.get_session(session_id)
            if not session:
                await self._send_error(websocket, session_id, 3001,
                                     "session_not_found", "Session not found")
                return

            # 处理音频数据
            result = await self.session_manager.process_audio_data(session_id, audio_msg)

            if result.get("error"):
                await self._send_error(websocket, session_id, result["error_code"],
                                     "audio_processing_error", result["error"])
                return

            # 发送识别结果（如果有）
            if result.get("text"):
                response = WebSocketProtocol.create_recognition_result(
                    session_id=session_id,
                    sequence_id=audio_msg.sequence_id,
                    text=result["text"],
                    is_final=result.get("is_final", False),
                    confidence=result.get("confidence", 0.0),
                    language=result.get("language"),
                    processing_time=result.get("processing_time")
                )
                await websocket.send_text(response)

        except Exception as e:
            logger.error(f"Audio data handling error: {e}")
            await self._send_error(websocket, session_id, 2001,
                                 "audio_data_error", str(e))

    async def _handle_start_recording(self, websocket: WebSocket, session_id: str):
        """处理开始录音消息"""
        try:
            # 更新会话状态
            await self.session_manager.start_recording(session_id)

            # 更新连接状态
            client_id = self._get_client_id_by_session(session_id)
            if client_id:
                self.connection_states[client_id] = ConnectionState.RECORDING

            # 发送状态更新
            status_msg = WebSocketProtocol.create_status_update(
                session_id, ConnectionState.RECORDING, "Recording started"
            )
            await websocket.send_text(status_msg)

        except Exception as e:
            logger.error(f"Start recording error: {e}")
            await self._send_error(websocket, session_id, 4001,
                                 "start_recording_error", str(e))

    async def _handle_stop_recording(self, websocket: WebSocket, session_id: str):
        """处理停止录音消息"""
        try:
            # 停止录音并获取最终结果
            final_result = await self.session_manager.stop_recording(session_id)

            # 更新连接状态
            client_id = self._get_client_id_by_session(session_id)
            if client_id:
                self.connection_states[client_id] = ConnectionState.CONNECTED

            # 发送最终结果（如果有）
            if final_result and final_result.get("text"):
                response = WebSocketProtocol.create_recognition_result(
                    session_id=session_id,
                    sequence_id=final_result.get("sequence_id", 0),
                    text=final_result["text"],
                    is_final=True,
                    confidence=final_result.get("confidence", 0.0),
                    language=final_result.get("language"),
                    processing_time=final_result.get("processing_time")
                )
                await websocket.send_text(response)

            # 发送状态更新
            status_msg = WebSocketProtocol.create_status_update(
                session_id, ConnectionState.CONNECTED, "Recording stopped"
            )
            await websocket.send_text(status_msg)

        except Exception as e:
            logger.error(f"Stop recording error: {e}")
            await self._send_error(websocket, session_id, 4001,
                                 "stop_recording_error", str(e))

    async def _handle_disconnect_request(self, websocket: WebSocket, session_id: str):
        """处理断开连接请求"""
        try:
            # 发送断开连接响应
            response = WebSocketProtocol.create_message(
                MessageType.DISCONNECT_RESPONSE,
                {"session_id": session_id, "status": "success", "message": "Disconnecting"}
            )
            await websocket.send_text(response)

            logger.info(f"Disconnect request handled for session: {session_id}")

        except Exception as e:
            logger.error(f"Disconnect request error: {e}")

    async def _handle_heartbeat(self, websocket: WebSocket, session_id: str):
        """处理心跳消息"""
        try:
            # 发送心跳响应
            response = WebSocketProtocol.create_message(
                MessageType.HEARTBEAT,
                {"session_id": session_id, "timestamp": time.time()}
            )
            await websocket.send_text(response)

        except Exception as e:
            logger.error(f"Heartbeat error: {e}")

    async def _heartbeat_loop(self, websocket: WebSocket, session_id: str):
        """心跳循环"""
        try:
            while True:
                await asyncio.sleep(self.heartbeat_interval)

                # 检查连接是否还活跃
                if session_id not in [s.session_id for s in self.session_manager.get_all_sessions()]:
                    break

                # 发送心跳
                try:
                    heartbeat_msg = WebSocketProtocol.create_message(
                        MessageType.HEARTBEAT,
                        {"session_id": session_id, "timestamp": time.time()}
                    )
                    await websocket.send_text(heartbeat_msg)
                except:
                    # 连接已断开
                    break

        except asyncio.CancelledError:
            logger.debug(f"Heartbeat loop cancelled for session: {session_id}")
        except Exception as e:
            logger.error(f"Heartbeat loop error: {e}")

    async def _send_error(
        self,
        websocket: WebSocket,
        session_id: Optional[str],
        error_code: int,
        error_type: str,
        message: str
    ):
        """发送错误消息"""
        try:
            error_msg = WebSocketProtocol.create_error_message(
                session_id, error_code, error_type, message
            )
            await websocket.send_text(error_msg)
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

    async def _cleanup_connection(self, client_id: str, session_id: Optional[str]):
        """清理连接资源"""
        try:
            # 移除活跃连接
            if client_id in self.active_connections:
                del self.active_connections[client_id]

            # 移除连接状态
            if client_id in self.connection_states:
                del self.connection_states[client_id]

            # 清理会话
            if session_id:
                await self.session_manager.cleanup_session(session_id)

            logger.info(f"Connection cleanup completed for client: {client_id}")

        except Exception as e:
            logger.error(f"Connection cleanup error: {e}")

    def _get_client_id_by_session(self, session_id: str) -> Optional[str]:
        """根据会话ID获取客户端ID"""
        session = self.session_manager.get_session(session_id)
        return session.client_id if session else None

    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "active_connections": len(self.active_connections),
            "connection_states": {
                client_id: state.value
                for client_id, state in self.connection_states.items()
            },
            "sessions": self.session_manager.get_session_count()
        }
