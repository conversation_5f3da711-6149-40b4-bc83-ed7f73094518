# Enhanced Stream ASR Server

基于Individual版本优化的增强流式语音识别服务器

## 主要特性

1. **Web界面支持** - 浏览器麦克风输入
2. **三阶段WebSocket协议** - 握手、数据传输、断开
3. **可配置分隔符** - 支持多语种自定义分隔符
4. **自动语种识别** - VAD + LID + ASR 完整流程
5. **模块化架构** - 高度可扩展的设计

## 项目结构

```
enhanced_stream_asr/
├── core/                    # 核心模块
│   ├── engines/            # 推理引擎
│   ├── session/            # 会话管理
│   ├── audio/              # 音频处理
│   ├── lid/                # 语种识别
│   └── asr/                # 语音识别
├── api/                     # API接口
│   ├── websocket/          # WebSocket处理
│   └── http/               # HTTP接口
├── utils/                   # 工具模块
│   ├── config/             # 配置管理
│   ├── monitoring/         # 监控指标
│   └── logger.py           # 日志工具
├── web/                     # Web界面
│   ├── static/             # 静态资源
│   └── templates/          # 模板文件
├── configs/                 # 配置文件
│   ├── lang_configs/       # 语种配置
│   └── server_config.yaml  # 服务器配置
└── server.py               # 主服务器
```

## 快速开始

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务器
python server.py

# 访问Web界面
http://localhost:8080
```

## 配置说明

详见 `configs/` 目录下的配置文件。

## API文档

详见 API 文档部分。
