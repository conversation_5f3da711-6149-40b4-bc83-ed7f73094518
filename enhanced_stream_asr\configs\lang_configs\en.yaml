# 英文语种配置

code: "en"
name: "English"

# 分隔符设置
separator: ", "                   # 英文半角逗号+空格
silence_threshold: 0.4           # 静音阈值（秒）

# 模型配置
model:
  model_path: "models/en/encoder.onnx"
  ctc_path: "models/en/ctc.onnx"
  decoder_path: "models/en/decoder.onnx"
  vocabulary_path: "models/en/units.txt"
  
  # 解码参数
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7
  
  # 设备设置
  device: "cpu"
  quantized: true

# 特征配置
features:
  enable_punctuation: true      # 启用标点符号预测
  enable_itn: true             # 启用逆文本标准化
  enable_hotwords: false       # 英文暂不支持热词
  enable_modal_particle_filter: false

# 后处理配置
postprocess:
  enable_text_normalization: true
  enable_number_conversion: true
  enable_time_conversion: true
  enable_date_conversion: true
  enable_capitalization: true  # 启用首字母大写

# 性能配置
performance:
  max_sentence_silence: 500    # 最大句子静音时长（毫秒）
  blank_interval: 0.5          # 空白间隔（秒）
