"""
WebSocket三阶段连接协议定义
定义握手、数据传输、断开连接的完整协议
"""

import json
import logging
from enum import Enum
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """消息类型枚举"""
    # 握手阶段
    HANDSHAKE_REQUEST = "handshake_request"
    HANDSHAKE_RESPONSE = "handshake_response"
    
    # 数据传输阶段
    AUDIO_DATA = "audio_data"
    RECOGNITION_RESULT = "recognition_result"
    INTERMEDIATE_RESULT = "intermediate_result"
    
    # 控制消息
    START_RECORDING = "start_recording"
    STOP_RECORDING = "stop_recording"
    HEARTBEAT = "heartbeat"
    
    # 断开阶段
    DISCONNECT_REQUEST = "disconnect_request"
    DISCONNECT_RESPONSE = "disconnect_response"
    
    # 错误消息
    ERROR = "error"
    
    # 状态消息
    STATUS_UPDATE = "status_update"
    SESSION_INFO = "session_info"


class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    HANDSHAKING = "handshaking"
    CONNECTED = "connected"
    RECORDING = "recording"
    PROCESSING = "processing"
    DISCONNECTING = "disconnecting"


@dataclass
class HandshakeRequest:
    """握手请求消息"""
    client_id: str
    language: Optional[str] = None
    auto_language_detection: bool = True
    sample_rate: int = 16000
    channels: int = 1
    sample_width: int = 2
    chunk_duration: float = 0.4  # 秒
    enable_intermediate_result: bool = True
    enable_punctuation: bool = True
    enable_itn: bool = True
    custom_separator: Optional[str] = None
    hotwords: Optional[list] = None
    client_info: Optional[Dict[str, Any]] = None


@dataclass
class HandshakeResponse:
    """握手响应消息"""
    session_id: str
    status: str  # "success" or "error"
    message: str
    server_config: Optional[Dict[str, Any]] = None
    supported_languages: Optional[list] = None
    error_code: Optional[int] = None


@dataclass
class AudioDataMessage:
    """音频数据消息"""
    session_id: str
    sequence_id: int
    audio_data: str  # base64编码的音频数据
    is_final: bool = False
    timestamp: Optional[float] = None


@dataclass
class RecognitionResult:
    """识别结果消息"""
    session_id: str
    sequence_id: int
    text: str
    is_final: bool
    confidence: float = 0.0
    language: Optional[str] = None
    segments: Optional[list] = None
    processing_time: Optional[float] = None
    timestamp: Optional[float] = None


@dataclass
class ErrorMessage:
    """错误消息"""
    session_id: Optional[str]
    error_code: int
    error_type: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[float] = None


@dataclass
class StatusUpdate:
    """状态更新消息"""
    session_id: str
    state: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[float] = None


class WebSocketProtocol:
    """WebSocket协议处理器"""
    
    # 错误码定义
    ERROR_CODES = {
        # 握手阶段错误 (1000-1999)
        1001: "Invalid handshake request format",
        1002: "Missing required handshake parameters",
        1003: "Unsupported language",
        1004: "Invalid audio configuration",
        1005: "Server capacity exceeded",
        
        # 数据传输阶段错误 (2000-2999)
        2001: "Invalid audio data format",
        2002: "Audio data size mismatch",
        2003: "Sequence ID error",
        2004: "Audio data decoding failed",
        2005: "Sample rate mismatch",
        2006: "Processing timeout",
        
        # 会话管理错误 (3000-3999)
        3001: "Session not found",
        3002: "Session expired",
        3003: "Invalid session state",
        3004: "Session limit exceeded",
        
        # 系统错误 (4000-4999)
        4001: "Internal server error",
        4002: "Service unavailable",
        4003: "Model loading failed",
        4004: "Resource exhausted",
        
        # 协议错误 (5000-5999)
        5001: "Invalid message format",
        5002: "Unsupported message type",
        5003: "Protocol violation",
        5004: "Connection timeout"
    }
    
    @staticmethod
    def create_message(msg_type: MessageType, data: Union[Dict, Any]) -> str:
        """
        创建协议消息
        
        Args:
            msg_type: 消息类型
            data: 消息数据
            
        Returns:
            str: JSON格式的消息字符串
        """
        try:
            if hasattr(data, '__dict__'):
                # 如果是dataclass对象，转换为字典
                data_dict = asdict(data)
            elif isinstance(data, dict):
                data_dict = data
            else:
                data_dict = {"data": data}
                
            message = {
                "type": msg_type.value,
                "timestamp": datetime.now().timestamp(),
                **data_dict
            }
            
            return json.dumps(message, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Failed to create message: {e}")
            raise ValueError(f"Message creation failed: {e}")
            
    @staticmethod
    def parse_message(message_str: str) -> tuple[MessageType, Dict[str, Any]]:
        """
        解析协议消息
        
        Args:
            message_str: JSON格式的消息字符串
            
        Returns:
            tuple: (消息类型, 消息数据)
        """
        try:
            data = json.loads(message_str)
            
            if "type" not in data:
                raise ValueError("Missing message type")
                
            msg_type = MessageType(data["type"])
            
            # 移除type字段，返回纯数据
            message_data = {k: v for k, v in data.items() if k != "type"}
            
            return msg_type, message_data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise ValueError(f"Invalid JSON format: {e}")
        except ValueError as e:
            logger.error(f"Message parsing error: {e}")
            raise ValueError(f"Invalid message format: {e}")
            
    @staticmethod
    def create_handshake_response(
        session_id: str, 
        success: bool, 
        message: str = "",
        server_config: Optional[Dict] = None,
        error_code: Optional[int] = None
    ) -> str:
        """创建握手响应消息"""
        response = HandshakeResponse(
            session_id=session_id,
            status="success" if success else "error",
            message=message,
            server_config=server_config,
            error_code=error_code
        )
        return WebSocketProtocol.create_message(MessageType.HANDSHAKE_RESPONSE, response)
        
    @staticmethod
    def create_recognition_result(
        session_id: str,
        sequence_id: int,
        text: str,
        is_final: bool,
        confidence: float = 0.0,
        language: Optional[str] = None,
        processing_time: Optional[float] = None
    ) -> str:
        """创建识别结果消息"""
        result = RecognitionResult(
            session_id=session_id,
            sequence_id=sequence_id,
            text=text,
            is_final=is_final,
            confidence=confidence,
            language=language,
            processing_time=processing_time,
            timestamp=datetime.now().timestamp()
        )
        
        msg_type = MessageType.RECOGNITION_RESULT if is_final else MessageType.INTERMEDIATE_RESULT
        return WebSocketProtocol.create_message(msg_type, result)
        
    @staticmethod
    def create_error_message(
        session_id: Optional[str],
        error_code: int,
        error_type: str = "general",
        message: str = "",
        details: Optional[Dict] = None
    ) -> str:
        """创建错误消息"""
        if not message and error_code in WebSocketProtocol.ERROR_CODES:
            message = WebSocketProtocol.ERROR_CODES[error_code]
            
        error = ErrorMessage(
            session_id=session_id,
            error_code=error_code,
            error_type=error_type,
            message=message,
            details=details,
            timestamp=datetime.now().timestamp()
        )
        return WebSocketProtocol.create_message(MessageType.ERROR, error)
        
    @staticmethod
    def create_status_update(
        session_id: str,
        state: ConnectionState,
        message: str = "",
        details: Optional[Dict] = None
    ) -> str:
        """创建状态更新消息"""
        status = StatusUpdate(
            session_id=session_id,
            state=state.value,
            message=message,
            details=details,
            timestamp=datetime.now().timestamp()
        )
        return WebSocketProtocol.create_message(MessageType.STATUS_UPDATE, status)
        
    @staticmethod
    def validate_handshake_request(data: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        验证握手请求
        
        Args:
            data: 握手请求数据
            
        Returns:
            tuple: (是否有效, 错误消息)
        """
        required_fields = ["client_id"]
        
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field: {field}"
                
        # 验证音频配置
        sample_rate = data.get("sample_rate", 16000)
        if sample_rate not in [8000, 16000, 44100, 48000]:
            return False, f"Unsupported sample rate: {sample_rate}"
            
        channels = data.get("channels", 1)
        if channels not in [1, 2]:
            return False, f"Unsupported channel count: {channels}"
            
        return True, None
