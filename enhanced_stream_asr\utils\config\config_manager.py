"""
配置管理器
统一管理服务器配置、语种配置等
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "configs"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.server_config = {}
        self.language_configs = {}
        self.lid_config = {}
        
        self.load_all_configs()
        
    def load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 加载服务器配置
            self.load_server_config()
            
            # 加载语种配置
            self.load_language_configs()
            
            # 加载LID配置
            self.load_lid_config()
            
            logger.info("All configurations loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load configurations: {e}")
            raise
            
    def load_server_config(self):
        """加载服务器配置"""
        config_file = self.config_dir / "server_config.yaml"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self.server_config = yaml.safe_load(f) or {}
        else:
            # 使用默认配置
            self.server_config = self.get_default_server_config()
            logger.warning(f"Server config file not found, using defaults: {config_file}")
            
    def load_language_configs(self):
        """加载语种配置"""
        lang_config_dir = self.config_dir / "lang_configs"
        
        if not lang_config_dir.exists():
            logger.warning(f"Language config directory not found: {lang_config_dir}")
            return
            
        for config_file in lang_config_dir.glob("*.yaml"):
            lang_code = config_file.stem
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
                    self.language_configs[lang_code] = config
                    logger.debug(f"Loaded language config for: {lang_code}")
                    
            except Exception as e:
                logger.error(f"Failed to load language config {config_file}: {e}")
                
    def load_lid_config(self):
        """加载LID配置"""
        config_file = self.config_dir / "lid_config.yaml"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self.lid_config = yaml.safe_load(f) or {}
        else:
            # 使用默认LID配置
            self.lid_config = self.get_default_lid_config()
            logger.warning(f"LID config file not found, using defaults: {config_file}")
            
    def get_default_server_config(self) -> Dict[str, Any]:
        """获取默认服务器配置"""
        return {
            "server": {
                "host": "0.0.0.0",
                "port": 8080,
                "workers": 1
            },
            "websocket": {
                "heartbeat_interval": 30,
                "max_connections": 100,
                "connection_timeout": 300
            },
            "audio": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_duration": 0.4,
                "max_audio_duration": 60
            },
            "asr": {
                "supported_languages": ["zh", "en", "ru", "ug", "kk"],
                "default_language": "zh",
                "enable_auto_language_detection": True,
                "enable_intermediate_result": True,
                "enable_punctuation": True,
                "enable_itn": True
            },
            "models": {
                "model_dir": "models",
                "device": "cpu",
                "quantized": True
            }
        }
        
    def get_default_lid_config(self) -> Dict[str, Any]:
        """获取默认LID配置"""
        return {
            "model": {
                "model_path": "models/lid/lid_model.onnx",
                "device": "cpu"
            },
            "detection": {
                "min_audio_duration": 0.4,
                "max_audio_duration": 2.4,
                "confidence_threshold": 0.8,
                "progressive_steps": [0.4, 0.8, 1.2, 1.6, 2.0, 2.4]
            },
            "languages": {
                "supported": ["zh", "en", "ru", "ug", "kk"],
                "fallback": "zh"
            }
        }
        
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值（支持点号分隔的嵌套键）
        
        Args:
            key: 配置键，支持 "section.subsection.key" 格式
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.server_config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
                    
            return value
            
        except Exception:
            return default
            
    def get_language_config(self, lang_code: str) -> Dict[str, Any]:
        """
        获取语种配置
        
        Args:
            lang_code: 语种代码
            
        Returns:
            语种配置字典
        """
        return self.language_configs.get(lang_code, {})
        
    def get_separator(self, lang_code: str, custom_separator: Optional[str] = None) -> str:
        """
        获取语种分隔符
        
        Args:
            lang_code: 语种代码
            custom_separator: 自定义分隔符（优先级最高）
            
        Returns:
            分隔符字符串
        """
        # 优先使用自定义分隔符
        if custom_separator:
            return custom_separator
            
        # 从语种配置中获取
        lang_config = self.get_language_config(lang_code)
        if 'separator' in lang_config:
            return lang_config['separator']
            
        # 使用默认分隔符
        default_separators = {
            'zh': '，',      # 中文全角逗号
            'en': ', ',      # 英文半角逗号+空格
            'ru': ', ',      # 俄语半角逗号+空格
            'ug': '، ',      # 维语逗号+空格
            'kk': ', '       # 哈萨克语半角逗号+空格
        }
        
        return default_separators.get(lang_code, ', ')
        
    def get_silence_threshold(self, lang_code: str) -> float:
        """
        获取语种静音阈值
        
        Args:
            lang_code: 语种代码
            
        Returns:
            静音阈值（秒）
        """
        lang_config = self.get_language_config(lang_code)
        return lang_config.get('silence_threshold', 0.35)
        
    def get_lid_config(self) -> Dict[str, Any]:
        """获取LID配置"""
        return self.lid_config
        
    def get_supported_languages(self) -> list:
        """获取支持的语种列表"""
        return self.get("asr.supported_languages", ["zh", "en"])
        
    def is_language_supported(self, lang_code: str) -> bool:
        """检查语种是否支持"""
        return lang_code in self.get_supported_languages()
        
    def get_model_config(self, lang_code: str) -> Dict[str, Any]:
        """
        获取语种模型配置
        
        Args:
            lang_code: 语种代码
            
        Returns:
            模型配置字典
        """
        lang_config = self.get_language_config(lang_code)
        model_config = lang_config.get('model', {})
        
        # 合并全局模型配置
        global_model_config = self.get('models', {})
        
        # 语种特定配置优先
        return {**global_model_config, **model_config}
        
    def save_config(self, config_type: str, config_data: Dict[str, Any]):
        """
        保存配置
        
        Args:
            config_type: 配置类型 ('server', 'lid', 或语种代码)
            config_data: 配置数据
        """
        try:
            if config_type == 'server':
                config_file = self.config_dir / "server_config.yaml"
                self.server_config = config_data
            elif config_type == 'lid':
                config_file = self.config_dir / "lid_config.yaml"
                self.lid_config = config_data
            else:
                # 语种配置
                config_file = self.config_dir / "lang_configs" / f"{config_type}.yaml"
                config_file.parent.mkdir(parents=True, exist_ok=True)
                self.language_configs[config_type] = config_data
                
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                
            logger.info(f"Configuration saved: {config_file}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
            
    def reload_configs(self):
        """重新加载所有配置"""
        self.load_all_configs()
        logger.info("Configurations reloaded")
